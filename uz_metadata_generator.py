#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
uzVideo 扩展元数据生成器 - Python 版本
用于定时检测目标仓库更新并生成配置文件
"""

import json
import re
import zipfile
import shutil
import requests
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging

# 文件名配置
FILE_CONFIG = {
    'log_file': 'uz_generator.log',
    'commit_file': 'last_target_commit.txt',
    'temp_zip': 'temp_repo.zip',
    'temp_extract_dir': 'temp_extract',
    'main_zip': 'uzAio_green.zip',
    'douer_v2_zip': 'douer_v2.zip',
    'douer_v3_zip': 'douer_v3.zip',
    'local_json': 'local.json',
    'env_json': 'env.json'
}

# 配置日志
def setup_logging():
    """配置日志系统"""
    log_file = Path(__file__).parent / FILE_CONFIG['log_file']
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()

# ==================== 配置常量 ====================

# 目标仓库配置
TARGET_REPO = {
    'owner': 'YYDS678',
    'repo': 'uzVideo-extensions',
    'branch': 'main'
}

# 类型映射
TYPE_MAPPING = {
    'danMu/js': 400,
    'panTools/js': 300,
    'recommend/js': 200,
    'vod/js': 101,
}

# 表情符号映射（用于douer_v2版本）
EMOJI_MAP = {
    '玩偶': '🧸', '木偶': '🪵', '闪电': '⚡', '蜡笔': '🖍️',
    '虎斑': '🐯', '二小': '🐮', '至臻': '💎', '欧歌': '🎶',
    '多多': '🐼', '校长': '🎓', '茉莉': '🎬', '南风': '💨',
    '雷鲸': '🐋', '趣盘': '🔎', '逸搜': '🔎', 'TG搜': '🔎', 'TG纯搜': '🔎'
}

# 网络请求配置
REQUEST_CONFIG = {
    'timeout': 30,
    'download_timeout': 300,
    'retry_times': 3
}

# 文件过滤配置
FILTER_CONFIG = {
    # 要包含在ZIP中的目录
    'include_dirs': ['danMu', 'panTools', 'recommend', 'vod', 'live', 'cms'],
    # 要包含在ZIP中的文件
    'include_files': ['local.json', 'env.json'],
    # 要排除的JSON文件
    'exclude_json_files': ['douban.json', 'panTools.json', 'danMu.json', 'vod.json'],
    # 要排除的文件名
    'exclude_filenames': ['README.md']
}



# Git配置
GIT_CONFIG = {
    'user_name': 'Python Auto Generator',
    'user_email': '<EMAIL>',
    'commit_message': '该文件自动生成，无需手动修改。'
}

# 目录配置
DIR_CONFIG = {
    'scan_dirs': ['danMu/js', 'panTools/js', 'recommend/js', 'vod/js'],
    'clean_dirs': ['danMu', 'panTools', 'recommend', 'vod', 'live', 'cms'],
    'douer_dirs': ['danMu', 'panTools', 'recommend', 'vod'],
    'source_order': ['panTools', 'danMu', 'recommend', 'live', 'vod']
}

# ==================== 主类定义 ====================

class UzMetadataGenerator:
    def __init__(self):
        # 使用全局配置
        self.TARGET_REPO = TARGET_REPO
        self.TYPE_MAPPING = TYPE_MAPPING
        self.emoji_map = EMOJI_MAP

        # 工作目录
        self.work_dir = Path(__file__).parent
        self.commit_file = self.work_dir / FILE_CONFIG['commit_file']



    def get_latest_commit(self) -> Optional[str]:
        """获取目标仓库最新提交哈希"""
        try:
            url = f"https://api.github.com/repos/{self.TARGET_REPO['owner']}/{self.TARGET_REPO['repo']}/commits/{self.TARGET_REPO['branch']}"
            response = requests.get(url, timeout=REQUEST_CONFIG['timeout'])
            response.raise_for_status()
            return response.json()['sha']
        except Exception as e:
            logger.error(f"获取最新提交失败: {e}")
            return None

    def check_need_update(self) -> bool:
        """检查是否需要更新"""
        latest_commit = self.get_latest_commit()
        if not latest_commit:
            return False
            
        if not self.commit_file.exists():
            logger.info("首次运行，需要更新")
            return True
            
        try:
            last_commit = self.commit_file.read_text().strip()
            if last_commit == latest_commit:
                logger.info("无更新，跳过处理")
                return False
            else:
                logger.info(f"发现更新: {last_commit[:8]} -> {latest_commit[:8]}")
                return True
        except Exception as e:
            logger.error(f"读取上次提交记录失败: {e}")
            return True

    def download_and_extract_repo(self) -> bool:
        """下载目标仓库并直接解压相关目录到工作目录"""
        try:
            # 清理旧的目标目录和相关文件夹
            directories_to_clean = DIR_CONFIG['clean_dirs']
            for dir_name in directories_to_clean:
                dir_path = self.work_dir / dir_name
                if dir_path.exists():
                    shutil.rmtree(dir_path)

            # 下载 ZIP 包
            zip_url = f"https://github.com/{self.TARGET_REPO['owner']}/{self.TARGET_REPO['repo']}/archive/{self.TARGET_REPO['branch']}.zip"
            logger.info(f"下载仓库: {zip_url}")

            response = requests.get(zip_url, timeout=REQUEST_CONFIG['download_timeout'])
            response.raise_for_status()

            # 保存并解压
            zip_path = self.work_dir / FILE_CONFIG['temp_zip']
            zip_path.write_bytes(response.content)

            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                # 解压到临时目录
                temp_extract_dir = self.work_dir / FILE_CONFIG['temp_extract_dir']
                if temp_extract_dir.exists():
                    shutil.rmtree(temp_extract_dir)
                zip_ref.extractall(temp_extract_dir)

                # 找到解压后的仓库目录
                extracted_repo_dir = temp_extract_dir / f"{self.TARGET_REPO['repo']}-{self.TARGET_REPO['branch']}"

                # 直接复制需要的目录到工作目录
                for dir_name in directories_to_clean:
                    src_dir = extracted_repo_dir / dir_name
                    dst_dir = self.work_dir / dir_name
                    if src_dir.exists():
                        shutil.copytree(src_dir, dst_dir)
                        logger.info(f"解压目录: {dir_name}")

                # 清理临时目录
                shutil.rmtree(temp_extract_dir)

            # 清理临时文件
            zip_path.unlink()

            logger.info("仓库下载解压完成")
            return True

        except Exception as e:
            logger.error(f"下载仓库失败: {e}")
            return False

    def extract_value(self, content: str, tag: str) -> str:
        """从内容中提取标签值"""
        pattern = rf'^.*{re.escape(tag)}(.*)$'
        match = re.search(pattern, content, re.MULTILINE)
        return match.group(1).strip() if match else ''

    def parse_file_metadata(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """解析文件元数据"""
        try:
            content = file_path.read_text(encoding='utf-8')
            
            # 检查是否废弃或AV内容
            deprecated = self.extract_value(content, '@deprecated:')
            if deprecated and int(deprecated) == 1:
                return None
                
            is_av = self.extract_value(content, '@isAV:')
            if is_av and int(is_av) == 1:
                return None
            
            # 提取元数据
            metadata = {
                'name': self.extract_value(content, '@name:'),
                'webSite': self.extract_value(content, '@webSite:'),
                'version': self.extract_value(content, '@version:'),
                'remark': self.extract_value(content, '@remark:'),
                'env': self.extract_value(content, '@env:'),
                'codeID': self.extract_value(content, '@codeID:'),
                'type': self.extract_value(content, '@type:'),
                'instance': self.extract_value(content, '@instance:'),
                'order': self.extract_value(content, '@order:'),
            }
            
            if not metadata['name']:
                return None
            
            # 生成API链接
            relative_path = file_path.relative_to(self.work_dir)
            # 统一使用正斜杠，确保跨平台兼容性
            dir_path = str(relative_path.parent).replace('\\', '/')
            relative_path_str = str(relative_path).replace('\\', '/')

            if not metadata['type'].strip():
                metadata['type'] = str(self.TYPE_MAPPING.get(dir_path, ''))

            # 使用目标仓库信息生成API链接
            api_url = f"https://raw.githubusercontent.com/{self.TARGET_REPO['owner']}/{self.TARGET_REPO['repo']}/{self.TARGET_REPO['branch']}/{relative_path_str}"
            metadata['api'] = api_url
            
            return metadata
            
        except Exception as e:
            logger.error(f"解析文件 {file_path} 失败: {e}")
            return None



    def scan_and_parse_files(self) -> Dict[str, List[Dict[str, Any]]]:
        """扫描并解析所有文件"""
        directories = DIR_CONFIG['scan_dirs']
        all_result = {}

        for dir_path in directories:
            # 使用Path对象处理路径，自动处理分隔符
            full_path = self.work_dir / Path(dir_path)
            if not full_path.exists():
                continue
                
            category = dir_path.split('/')[0]
            all_result[category] = []
            
            # 扫描JS和TXT文件
            for file_path in full_path.glob('*'):
                if file_path.suffix in ['.js', '.txt']:
                    metadata = self.parse_file_metadata(file_path)
                    if metadata:
                        item = {
                            'name': metadata['name'],
                            'api': metadata['api'],
                            'type': int(metadata['type']) if metadata['type'] else None,
                        }
                        
                        # 添加可选字段
                        for field in ['version', 'remark', 'env', 'webSite', 'codeID', 'instance', 'order']:
                            if metadata[field]:
                                if field == 'version':
                                    item[field] = int(metadata[field])
                                else:
                                    item[field] = metadata[field]
                        
                        all_result[category].append(item)
        
        return all_result

    def sort_by_order(self, items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """按order字段排序 - 完全匹配Node.js版本的排序逻辑"""
        def sort_key(item):
            order = item.get('order', '')
            name = item.get('name', '')

            if order:
                # 获取order的第一个字符(分类字母)
                order_char = order[0] if order else ''
                return (0, order_char, order, len(name), name)
            return (1, name)

        return sorted(items, key=sort_key)

    def run_git_command(self, command: List[str]) -> bool:
        """运行 Git 命令"""
        try:
            result = subprocess.run(
                command,
                cwd=self.work_dir,
                capture_output=True,
                text=True,
                timeout=60
            )

            if result.returncode == 0:
                logger.info(f"Git 命令执行成功: {' '.join(command)}")
                if result.stdout.strip():
                    logger.info(f"输出: {result.stdout.strip()}")
                return True
            else:
                logger.error(f"Git 命令执行失败: {' '.join(command)}")
                logger.error(f"错误: {result.stderr.strip()}")
                return False

        except subprocess.TimeoutExpired:
            logger.error(f"Git 命令超时: {' '.join(command)}")
            return False
        except Exception as e:
            logger.error(f"执行 Git 命令异常: {e}")
            return False

    def commit_and_push_changes(self):
        """提交并推送更改到 Git 仓库"""
        try:
            # 配置 Git 用户信息
            self.run_git_command(['git', 'config', 'user.name', GIT_CONFIG['user_name']])
            self.run_git_command(['git', 'config', 'user.email', GIT_CONFIG['user_email']])

            # 添加生成的文件
            files_to_add = [
                FILE_CONFIG['douer_v2_zip'], FILE_CONFIG['douer_v3_zip'],
                FILE_CONFIG['main_zip'], FILE_CONFIG['commit_file']
            ]

            # 添加存在的文件
            for filename in files_to_add:
                file_path = self.work_dir / filename
                if file_path.exists():
                    self.run_git_command(['git', 'add', filename])

            # 提交更改
            commit_message = GIT_CONFIG['commit_message']
            if self.run_git_command(['git', 'commit', '-m', commit_message]):
                # 推送到远程仓库
                if self.run_git_command(['git', 'push']):
                    logger.info("Git 提交推送成功")
                    return True
                else:
                    logger.error("Git 推送失败")
                    return False
            else:
                logger.info("没有更改需要提交")
                return True

        except Exception as e:
            logger.error(f"Git 操作失败: {e}")
            return False

    def save_commit_hash(self):
        """保存当前处理的提交哈希"""
        latest_commit = self.get_latest_commit()
        if latest_commit:
            self.commit_file.write_text(latest_commit)
            logger.info(f"保存提交哈希: {latest_commit[:8]}")

    def add_live_and_cms_data(self, all_result: Dict[str, List[Dict[str, Any]]]):
        """添加live和cms数据"""
        try:
            # 添加live数据
            live_file = self.work_dir / 'live' / 'live.json'
            if live_file.exists():
                live_data = json.loads(live_file.read_text(encoding='utf-8'))
                all_result['live'] = live_data

            # 添加cms数据到vod
            cms_file = self.work_dir / 'cms' / 'cms.json'
            if cms_file.exists():
                cms_data = json.loads(cms_file.read_text(encoding='utf-8'))
                if 'vod' not in all_result:
                    all_result['vod'] = []
                all_result['vod'].extend(cms_data)

        except Exception as e:
            logger.error(f"添加live/cms数据失败: {e}")

    def generate_config_files(self, all_result: Dict[str, List[Dict[str, Any]]]):
        """生成配置文件"""
        try:
            # 生成sources列表用于local.json和env.json（按指定顺序排列）
            sources = []
            for category in DIR_CONFIG['source_order']:
                if category in all_result:
                    sources.extend(all_result[category])

            # 生成local.json (本地路径版本)
            local_sources = self.convert_to_local_paths(sources)
            self.save_json_file(FILE_CONFIG['local_json'], local_sources)

            # 生成env.json (环境变量配置)
            env_list = self.extract_env_variables(sources)
            self.save_json_file(FILE_CONFIG['env_json'], env_list)

            # 生成douer系列ZIP包 (只包含[盘]类型)
            self.generate_douer_zips(all_result)

            logger.info("配置文件生成完成")

        except Exception as e:
            logger.error(f"生成配置文件失败: {e}")

    def convert_to_local_paths(self, sources: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """转换为本地路径"""
        import copy
        github_raw = f"https://raw.githubusercontent.com/{self.TARGET_REPO['owner']}/{self.TARGET_REPO['repo']}/{self.TARGET_REPO['branch']}/"

        # 深拷贝避免修改原始数据
        local_sources = copy.deepcopy(sources)
        for item in local_sources:
            if 'api' in item:
                item['api'] = item['api'].replace(github_raw, '')

        return local_sources

    def extract_env_variables(self, sources: List[Dict[str, Any]]) -> List[Dict[str, str]]:
        """提取环境变量"""
        env_list = []
        env_set = set()

        for item in sources:
            env_str = item.get('env', '')
            if env_str:
                env_pairs = env_str.split('&&')
                for env_pair in env_pairs:
                    parts = env_pair.split('##')
                    if len(parts) >= 2:
                        env_key = parts[0].strip()
                        if env_key not in env_set:
                            env_set.add(env_key)
                            env_list.append({
                                'name': env_key,
                                'desc': parts[1].strip(),
                                'value': ''
                            })

        return env_list

    def generate_douer_zips(self, all_result: Dict[str, List[Dict[str, Any]]]):
        """生成douer系列ZIP包"""
        try:
            # 过滤只包含[盘]类型的数据（排除live）
            pan_only_result = {
                'danMu': all_result.get('danMu', []),
                'panTools': all_result.get('panTools', []),
                'recommend': all_result.get('recommend', []),
                'vod': [item for item in all_result.get('vod', []) if item.get('name', '').find('[盘]') != -1]
            }

            # 生成v2版本（表情符号）
            pan_v2_result = json.loads(json.dumps(pan_only_result))
            for item in pan_v2_result['vod']:
                if item['name'].startswith('[盘] '):
                    base_name = item['name'].replace('[盘] ', '')
                    emoji = self.emoji_map.get(base_name, '📁')
                    item['name'] = emoji + base_name

            # 生成v3版本（统一格式 + 4K标识）
            pan_v3_result = json.loads(json.dumps(pan_only_result))
            for item in pan_v3_result['vod']:
                if item['name'].startswith('[盘] '):
                    base_name = item['name'].replace('[盘] ', '')
                    item['name'] = base_name + ' | 4K'

            # 创建ZIP包
            self.create_douer_zip(FILE_CONFIG['douer_v2_zip'], pan_v2_result)
            self.create_douer_zip(FILE_CONFIG['douer_v3_zip'], pan_v3_result)

            logger.info("douer系列ZIP包生成完成")

        except Exception as e:
            logger.error(f"生成douer ZIP包失败: {e}")

    def create_douer_zip(self, zip_name: str, douer_data: Dict[str, List[Dict[str, Any]]]):
        """创建douer专用ZIP包"""
        try:
            zip_path = self.work_dir / zip_name

            # 转换为本地路径格式（按指定顺序排列，排除live）
            sources = []
            for category in DIR_CONFIG['douer_dirs']:
                if category in douer_data:
                    sources.extend(douer_data[category])

            local_sources = self.convert_to_local_paths(sources)

            # 提取环境变量
            env_list = self.extract_env_variables(sources)

            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=9) as zipf:
                # 添加配置文件
                zipf.writestr(FILE_CONFIG['local_json'], json.dumps(local_sources, ensure_ascii=False, indent=2))
                zipf.writestr(FILE_CONFIG['env_json'], json.dumps(env_list, ensure_ascii=False, indent=2))

                # 添加目录（排除cms和live目录）
                include_dirs = DIR_CONFIG['douer_dirs']
                for dir_name in include_dirs:
                    dir_path = self.work_dir / dir_name
                    if dir_path.exists():
                        for file_path in dir_path.rglob('*'):
                            if file_path.is_file() and self.should_include_in_douer_zip(file_path, dir_name):
                                arc_name = file_path.relative_to(self.work_dir)
                                zipf.write(file_path, arc_name)

            logger.info(f"douer ZIP包创建完成: {zip_path}")

        except Exception as e:
            logger.error(f"创建douer ZIP包失败: {e}")

    def should_include_in_douer_zip(self, file_path: Path, dir_name: str) -> bool:
        """判断文件是否应该包含在douer ZIP中"""
        # 基础过滤
        if not self.should_include_in_zip(file_path):
            return False

        # 对于vod目录，只包含[盘]相关文件
        if dir_name == 'vod' and file_path.suffix in ['.js', '.txt']:
            try:
                content = file_path.read_text(encoding='utf-8')
                name = self.extract_value(content, '@name:')
                # 只包含名称中包含[盘]的文件
                return '[盘]' in name
            except Exception:
                return False

        return True

    def create_zip_package(self):
        """创建ZIP包"""
        try:
            zip_path = self.work_dir / FILE_CONFIG['main_zip']

            # 要包含的目录和文件
            include_dirs = FILTER_CONFIG['include_dirs']
            include_files = FILTER_CONFIG['include_files']

            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=9) as zipf:
                # 添加目录
                for dir_name in include_dirs:
                    dir_path = self.work_dir / dir_name
                    if dir_path.exists():
                        for file_path in dir_path.rglob('*'):
                            if file_path.is_file() and self.should_include_in_zip(file_path):
                                arc_name = file_path.relative_to(self.work_dir)
                                zipf.write(file_path, arc_name)

                # 添加配置文件
                for file_name in include_files:
                    file_path = self.work_dir / file_name
                    if file_path.exists():
                        zipf.write(file_path, file_name)

            logger.info(f"ZIP包创建完成: {zip_path}")

        except Exception as e:
            logger.error(f"创建ZIP包失败: {e}")

    def should_include_in_zip(self, file_path: Path) -> bool:
        """判断文件是否应该包含在ZIP中"""
        # 排除特定文件名
        if file_path.name in FILTER_CONFIG['exclude_filenames']:
            return False

        # 排除特定的JSON文件
        if file_path.name in FILTER_CONFIG['exclude_json_files']:
            return False

        # 对于JS和TXT文件，检查内容
        if file_path.suffix in ['.js', '.txt']:
            try:
                content = file_path.read_text(encoding='utf-8')
                deprecated = self.extract_value(content, '@deprecated:')
                is_av = self.extract_value(content, '@isAV:')

                # 过滤掉废弃或AV内容
                if (deprecated and int(deprecated) == 1) or (is_av and int(is_av) == 1):
                    return False
            except Exception:
                # 读取失败时默认包含
                pass

        return True



    def save_json_file(self, filename: str, data: Any):
        """保存JSON文件"""
        file_path = self.work_dir / filename
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info(f"保存文件: {filename}")

    def run(self, skip_git=False, force_update=False, cleanup_after=False):
        """主运行函数

        Args:
            skip_git (bool): 是否跳过Git操作，用于本地测试
            force_update (bool): 是否强制更新，跳过更新检查
            cleanup_after (bool): 是否在运行完成后清理下载的文件
        """
        if not force_update:
            logger.info("开始检查更新...")
            if not self.check_need_update():
                return
        else:
            logger.info("强制更新模式，跳过更新检查...")

        logger.info("开始下载并解压目标仓库...")
        if not self.download_and_extract_repo():
            return

        logger.info("扫描和解析文件...")
        all_result = self.scan_and_parse_files()

        # 添加live和cms数据
        self.add_live_and_cms_data(all_result)

        # 排序所有分类（live数据保持原始顺序）
        for category in all_result:
            if isinstance(all_result[category], list) and category != 'live':
                all_result[category] = self.sort_by_order(all_result[category])

        logger.info("生成配置文件...")
        self.generate_config_files(all_result)

        logger.info("创建ZIP包...")
        self.create_zip_package()

        if not skip_git:
            # 先保存commit hash，再进行Git操作
            self.save_commit_hash()
            logger.info("提交到Git仓库...")
            self.commit_and_push_changes()
        else:
            logger.info("跳过Git操作（本地测试模式）")

        # 运行完成后清理（可选）
        if cleanup_after:
            self.cleanup_downloaded_files()
            self.cleanup_generated_files()

        logger.info("处理完成!")

    def cleanup_downloaded_files(self):
        """清理下载解压的文件"""
        try:
            directories_to_clean = DIR_CONFIG['clean_dirs']
            for dir_name in directories_to_clean:
                dir_path = self.work_dir / dir_name
                if dir_path.exists():
                    shutil.rmtree(dir_path)
                    logger.info(f"清理目录: {dir_name}")
            logger.info("下载文件清理完成")
        except Exception as e:
            logger.error(f"清理下载文件失败: {e}")

    def cleanup_generated_files(self):
        """清理生成的配置文件"""
        try:
            files_to_clean = [FILE_CONFIG['local_json'], FILE_CONFIG['env_json']]
            for file_name in files_to_clean:
                file_path = self.work_dir / file_name
                if file_path.exists():
                    file_path.unlink()
                    logger.info(f"清理文件: {file_name}")
            logger.info("生成文件清理完成")
        except Exception as e:
            logger.error(f"清理生成文件失败: {e}")

if __name__ == '__main__':
    import sys

    # 检查命令行参数
    skip_git = '--skip-git' in sys.argv or '--test' in sys.argv
    force_update = '--force' in sys.argv
    cleanup_after = '--cleanup' in sys.argv

    generator = UzMetadataGenerator()
    generator.run(skip_git=skip_git, force_update=force_update, cleanup_after=cleanup_after)
