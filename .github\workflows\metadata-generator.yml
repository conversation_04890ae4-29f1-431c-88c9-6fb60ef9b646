name: Metadata JSON Generator

on:
  workflow_dispatch:  # 手动触发
  schedule:
    - cron: '0 */6 * * *'  # 每6小时检查一次

jobs:
  generate-metadata:
    runs-on: ubuntu-latest
    permissions:
      contents: write  # 确保 GITHUB_TOKEN 具有写入权限

    steps:
    - name: Checkout current repo
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # 获取完整的 Git 历史，避免 push 失败

    - name: Get target repo latest commit
      id: target-commit
      run: |
        # 获取目标仓库最新commit hash
        LATEST_COMMIT=$(curl -s "https://api.github.com/repos/YYDS678/uzVideo-extensions/commits/main" | jq -r '.sha')
        echo "latest_commit=$LATEST_COMMIT" >> $GITHUB_OUTPUT
        echo "Target repo latest commit: $LATEST_COMMIT"

    - name: Check if update needed
      id: check-update
      run: |
        # 检查是否存在上次的commit记录
        if [ -f "last_target_commit.txt" ]; then
          LAST_COMMIT=$(cat last_target_commit.txt)
          echo "Last processed commit: $LAST_COMMIT"
          echo "Current latest commit: ${{ steps.target-commit.outputs.latest_commit }}"

          if [ "$LAST_COMMIT" = "${{ steps.target-commit.outputs.latest_commit }}" ]; then
            echo "No updates found, skipping..."
            echo "need_update=false" >> $GITHUB_OUTPUT
          else
            echo "Updates found, proceeding..."
            echo "need_update=true" >> $GITHUB_OUTPUT
          fi
        else
          echo "First run, proceeding..."
          echo "need_update=true" >> $GITHUB_OUTPUT
        fi

    - name: Checkout target repo
      if: steps.check-update.outputs.need_update == 'true'
      uses: actions/checkout@v4
      with:
        repository: YYDS678/uzVideo-extensions
        ref: main
        path: target-repo

    - name: Copy target repo files
      if: steps.check-update.outputs.need_update == 'true'
      run: |
        # 复制目标仓库的相关目录到当前仓库
        cp -r target-repo/danMu ./danMu || true
        cp -r target-repo/panTools ./panTools || true
        cp -r target-repo/recommend ./recommend || true
        cp -r target-repo/vod ./vod || true
        cp -r target-repo/live ./live || true
        cp -r target-repo/cms ./cms || true

    - name: Set up Node.js
      if: steps.check-update.outputs.need_update == 'true'
      uses: actions/setup-node@v4
      with:
        node-version: '20'

    - name: Install dependencies
      if: steps.check-update.outputs.need_update == 'true'
      run: npm install

    - name: Generate Metadata
      if: steps.check-update.outputs.need_update == 'true'
      run: |
        node action/parse-metadata.cjs

    - name: Save current commit hash
      if: steps.check-update.outputs.need_update == 'true'
      run: |
        # 保存当前处理的commit hash
        echo "${{ steps.target-commit.outputs.latest_commit }}" > last_target_commit.txt

    - name: Commit changes
      if: steps.check-update.outputs.need_update == 'true'
      run: |
        git config --global user.name 'GitHub Action'
        git config --global user.email '<EMAIL>'
        git add local.json env.json
        git add douer_v2.json douer_v2_raw.json douer_v3.json douer_v3_raw.json
        git add uzAio_green.zip last_target_commit.txt
        git commit -m '该文件自动生成，无需手动修改。' || echo 'No changes to commit'
        git push
