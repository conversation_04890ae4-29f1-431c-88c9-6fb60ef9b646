2025-07-27 11:02:59,328 - INFO - 开始检查更新...
2025-07-27 11:02:59,953 - INFO - 首次运行，需要更新
2025-07-27 11:02:59,954 - INFO - 开始下载并解压目标仓库...
2025-07-27 11:02:59,954 - INFO - 下载仓库: https://github.com/YYDS678/uzVideo-extensions/archive/main.zip
2025-07-27 11:03:01,552 - INFO - 解压目录: danMu
2025-07-27 11:03:01,557 - INFO - 解压目录: panTools
2025-07-27 11:03:01,561 - INFO - 解压目录: recommend
2025-07-27 11:03:01,613 - INFO - 解压目录: vod
2025-07-27 11:03:01,614 - INFO - 解压目录: live
2025-07-27 11:03:01,616 - INFO - 解压目录: cms
2025-07-27 11:03:01,643 - INFO - 仓库下载解压完成
2025-07-27 11:03:01,643 - INFO - 扫描和解析文件...
2025-07-27 11:03:01,704 - INFO - 生成配置文件...
2025-07-27 11:03:01,705 - INFO - 保存文件: local.json
2025-07-27 11:03:01,706 - INFO - 保存文件: env.json
2025-07-27 11:03:01,801 - INFO - douer ZIP包创建完成: C:\Users\<USER>\Desktop\uz自写视频源\脚本\精简修改自用版本\douer_v2.zip
2025-07-27 11:03:01,898 - INFO - douer ZIP包创建完成: C:\Users\<USER>\Desktop\uz自写视频源\脚本\精简修改自用版本\douer_v3.zip
2025-07-27 11:03:01,898 - INFO - douer系列ZIP包生成完成
2025-07-27 11:03:01,898 - INFO - 配置文件生成完成
2025-07-27 11:03:01,899 - INFO - 创建ZIP包...
2025-07-27 11:03:02,002 - INFO - ZIP包创建完成: C:\Users\<USER>\Desktop\uz自写视频源\脚本\精简修改自用版本\uzAio_green.zip
2025-07-27 11:03:02,002 - INFO - 跳过Git操作（本地测试模式）
2025-07-27 11:03:02,002 - INFO - 处理完成!
2025-07-27 11:03:30,943 - INFO - 开始检查更新...
2025-07-27 11:03:31,555 - INFO - 首次运行，需要更新
2025-07-27 11:03:31,555 - INFO - 开始下载并解压目标仓库...
2025-07-27 11:03:31,575 - INFO - 下载仓库: https://github.com/YYDS678/uzVideo-extensions/archive/main.zip
2025-07-27 11:03:32,960 - INFO - 解压目录: danMu
2025-07-27 11:03:32,966 - INFO - 解压目录: panTools
2025-07-27 11:03:32,970 - INFO - 解压目录: recommend
2025-07-27 11:03:33,010 - INFO - 解压目录: vod
2025-07-27 11:03:33,012 - INFO - 解压目录: live
2025-07-27 11:03:33,013 - INFO - 解压目录: cms
2025-07-27 11:03:33,039 - INFO - 仓库下载解压完成
2025-07-27 11:03:33,040 - INFO - 扫描和解析文件...
2025-07-27 11:03:33,098 - INFO - 生成配置文件...
2025-07-27 11:03:33,099 - INFO - 保存文件: local.json
2025-07-27 11:03:33,100 - INFO - 保存文件: env.json
2025-07-27 11:03:33,197 - INFO - douer ZIP包创建完成: C:\Users\<USER>\Desktop\uz自写视频源\脚本\精简修改自用版本\douer_v2.zip
2025-07-27 11:03:33,294 - INFO - douer ZIP包创建完成: C:\Users\<USER>\Desktop\uz自写视频源\脚本\精简修改自用版本\douer_v3.zip
2025-07-27 11:03:33,294 - INFO - douer系列ZIP包生成完成
2025-07-27 11:03:33,294 - INFO - 配置文件生成完成
2025-07-27 11:03:33,294 - INFO - 创建ZIP包...
2025-07-27 11:03:33,398 - INFO - ZIP包创建完成: C:\Users\<USER>\Desktop\uz自写视频源\脚本\精简修改自用版本\uzAio_green.zip
2025-07-27 11:03:33,399 - INFO - 跳过Git操作（本地测试模式）
2025-07-27 11:03:33,400 - INFO - 清理目录: danMu
2025-07-27 11:03:33,402 - INFO - 清理目录: panTools
2025-07-27 11:03:33,403 - INFO - 清理目录: recommend
2025-07-27 11:03:33,419 - INFO - 清理目录: vod
2025-07-27 11:03:33,420 - INFO - 清理目录: live
2025-07-27 11:03:33,421 - INFO - 清理目录: cms
2025-07-27 11:03:33,421 - INFO - 下载文件清理完成
2025-07-27 11:03:33,421 - INFO - 清理文件: local.json
2025-07-27 11:03:33,421 - INFO - 清理文件: env.json
2025-07-27 11:03:33,421 - INFO - 生成文件清理完成
2025-07-27 11:03:33,422 - INFO - 处理完成!
