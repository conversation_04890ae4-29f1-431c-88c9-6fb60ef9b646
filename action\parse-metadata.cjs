const fs = require('fs')
const path = require('path')
const child_process = require('child_process')
const archiver = require('archiver')

const TYPE_MAPPING = {
  'danMu/js': 400,
  'panTools/js': 300,
  'recommend/js': 200,
  'vod/js': 101,
}

const kLocalPathTAG = '_localPathTAG_'

// 目标仓库配置
const TARGET_REPO = {
  owner: 'YYDS678',
  repo: 'uzVideo-extensions',
  branch: 'main'
}

const getRepoInfo = () => {
  // 对于API链接生成，使用目标仓库信息
  return [TARGET_REPO.owner, TARGET_REPO.repo]
}

const parseComments = (filePath) => {
  const content = fs.readFileSync(filePath, 'utf8')
  const deprecated = extractValue(content, '@deprecated:')
  if (deprecated && parseInt(deprecated) == 1) return null

  const isAV = extractValue(content, '@isAV:')
  if (isAV && parseInt(isAV) == 1) return null

  const metadata = {
    name: extractValue(content, '@name:'),
    webSite: extractValue(content, '@webSite:'),
    version: extractValue(content, '@version:'),
    remark: extractValue(content, '@remark:'),
    env: extractValue(content, '@env:'),
    codeID: extractValue(content, '@codeID:'),
    type: extractValue(content, '@type:'),
    instance: extractValue(content, '@instance:'),
    order: extractValue(content, '@order:'),
  }

  if (!metadata.name) return null

  const relativePath = path.relative(process.cwd(), filePath)
  const dirPath = path.dirname(relativePath)
  if (!metadata.type?.trim()) {
    metadata.type = TYPE_MAPPING[dirPath]
  }
  // 使用目标仓库的分支名称
  const branch = TARGET_REPO.branch
  const [owner, repo] = getRepoInfo()
  metadata.api = `https://raw.githubusercontent.com/${owner}/${repo}/${branch}/${kLocalPathTAG}${relativePath}`
  return metadata
}

const extractValue = (content, tag) => {
  const regex = new RegExp(`^.*${tag}(.*)$`, 'm')
  const match = content.match(regex)
  return match ? match[1].trim() : ''
}
const main = async () => {
  const directories = ['danMu/js', 'panTools/js', 'recommend/js', 'vod/js']
  const allInOneResult = {}

  directories.forEach((dir) => {
    const fullPath = path.join(__dirname, '..', dir)
    if (!fs.existsSync(fullPath)) return

    const files = fs
      .readdirSync(fullPath)
      .filter((f) => f.endsWith('.js') || f.endsWith('.txt'))
      .map((f) => ({
        file: f,
        stat: fs.statSync(path.join(fullPath, f)),
      }))
      // .sort((a, b) => b.stat.mtimeMs - a.stat.mtimeMs)
      .map((f) => f.file)

    files.forEach((file) => {
      const filePath = path.join(fullPath, file)
      const metadata = parseComments(filePath)
      if (metadata) {
        const item = {
          name: metadata.name,
          ...(metadata.version && {
            version: parseInt(metadata.version),
          }),
          ...(metadata.remark && { remark: metadata.remark }),
          ...(metadata.env && { env: metadata.env }),
          ...(metadata.webSite && { webSite: metadata.webSite }),
          ...(metadata.codeID && { codeID: metadata.codeID }),
          ...(metadata.instance && { instance: metadata.instance }),
          ...(metadata.order && { order: metadata.order }),

          api: metadata.api,
          type: parseInt(metadata.type),
        }
        const category = dir.split('/')[0]
        allInOneResult[category] = allInOneResult[category] || []
        allInOneResult[category].push(item)
      }
    })
  })

  // 定义排序函数
  const sortByOrder = (a, b) => {
    // 如果都有order，按order排序
    if (a.order && b.order) {
      // 获取order的第一个字符(分类字母)
      const aOrderChar = a.order.charAt(0);
      const bOrderChar = b.order.charAt(0);

      // 如果分类字母不同，按字母排序
      if (aOrderChar !== bOrderChar) {
        return aOrderChar.localeCompare(bOrderChar);
      }

      // 如果分类字母相同，按完整order值排序
      const orderCompare = a.order.localeCompare(b.order);
      if (orderCompare !== 0) {
        return orderCompare;
      }

      // 如果order值也相同，按名称长度从短到长排序
      if (a.name.length !== b.name.length) {
        return a.name.length - b.name.length;
      }

      // 最后按名称排序
      return a.name.localeCompare(b.name);
    }
    // 如果只有一个有order，有order的排前面
    if (a.order) return -1;
    if (b.order) return 1;
    // 否则按name排序
    return a.name.localeCompare(b.name);
  };

  // 为各个类别应用排序
  for (const category of Object.keys(allInOneResult)) {
    if (Array.isArray(allInOneResult[category])) {
      allInOneResult[category].sort(sortByOrder);
    }
  }

  const liveData = JSON.parse(fs.readFileSync('live/live.json', 'utf8'))
  allInOneResult.live = liveData

  const cmsData = JSON.parse(fs.readFileSync('cms/cms.json', 'utf8'))
  allInOneResult.vod.push(...cmsData)
  
  // 添加cms数据后再次排序vod
  allInOneResult.vod.sort(sortByOrder);

  let sources = [...allInOneResult.vod, ...allInOneResult.panTools, ...allInOneResult.recommend, ...allInOneResult.danMu, ...allInOneResult.live]

  // 使用 JSDelivr CDN 加速
  const [owner, repo] = getRepoInfo()
  const branch = TARGET_REPO.branch
  const githubRawHost = 'https://raw.githubusercontent.com'
  const jsdelivrCDN = `https://cdn.jsdelivr.net/gh/${owner}/${repo}@${branch}/`

  sources.forEach((item) => {
    // 将 https://raw.githubusercontent.com/user/repo/branch/path 转换为 https://cdn.jsdelivr.net/gh/user/repo@branch/path
    item.api = item.api.replace(`${githubRawHost}/${owner}/${repo}/${branch}/`, jsdelivrCDN)
  })

  // 生成只包含 [盘] 类型的版本（基于原始数据，使用 GitHub raw 链接）
  const panOnlyRawResult = {
    danMu: allInOneResult.danMu || [],
    panTools: allInOneResult.panTools || [],
    recommend: allInOneResult.recommend || [],
    vod: (allInOneResult.vod || []).filter(item => item.name && item.name.includes('[盘]')),
    live: allInOneResult.live || []
  }

  // 为 raw 版本恢复原始 GitHub 链接
  const restoreRawLinks = (data) => {
    const result = JSON.parse(JSON.stringify(data))
    const traverseAndRestore = (obj) => {
      if (Array.isArray(obj)) {
        obj.forEach(traverseAndRestore)
      } else if (obj && typeof obj === 'object') {
        if (obj.api && typeof obj.api === 'string') {
          obj.api = obj.api.replace(jsdelivrCDN, `${githubRawHost}/${owner}/${repo}/${branch}/`)
        }
        Object.values(obj).forEach(traverseAndRestore)
      }
    }
    traverseAndRestore(result)
    return result
  }

  const panOnlyRawResultWithRawLinks = restoreRawLinks(panOnlyRawResult)

  // 生成只包含 [盘] 类型的版本（使用 CDN 链接）
  const panOnlyResult = panOnlyRawResult

  // 表情符号映射表
  const emojiMap = {
    '玩偶': '🧸',
    '木偶': '🪵',
    '闪电': '⚡',
    '蜡笔': '🖍️',
    '虎斑': '🐯',
    '二小': '🐮',
    '至臻': '💎',
    '欧歌': '🎶',
    '多多': '🐼',
    '校长': '🎓',
    '茉莉': '🎬',
    '南风': '💨',
    '雷鲸': '🐋',
    '趣盘': '🔎',
    '逸搜': '🔎',
    'TG搜': '🔎',
    'TG纯搜': '🔎'
  }

  // 生成 v2 版本（表情符号）- raw 和 CDN 版本
  const panOnlyRawResultV2 = JSON.parse(JSON.stringify(panOnlyRawResultWithRawLinks))
  const panOnlyResultV2 = JSON.parse(JSON.stringify(panOnlyResult))

  ;[panOnlyRawResultV2, panOnlyResultV2].forEach(result => {
    result.vod.forEach(item => {
      if (item.name && item.name.startsWith('[盘] ')) {
        const baseName = item.name.replace('[盘] ', '')
        const emoji = emojiMap[baseName] || '📁'
        item.name = emoji + baseName
      }
    })
  })

  fs.writeFileSync('douer_v2_raw.json', JSON.stringify(panOnlyRawResultV2, null, 2))
  fs.writeFileSync('douer_v2.json', JSON.stringify(panOnlyResultV2, null, 2))

  // 生成 v3 版本（统一格式 + 4K标识）- raw 和 CDN 版本
  const panOnlyRawResultV3 = JSON.parse(JSON.stringify(panOnlyRawResultWithRawLinks))
  const panOnlyResultV3 = JSON.parse(JSON.stringify(panOnlyResult))

  ;[panOnlyRawResultV3, panOnlyResultV3].forEach(result => {
    result.vod.forEach(item => {
      if (item.name && item.name.startsWith('[盘] ')) {
        const baseName = item.name.replace('[盘] ', '')
        item.name = baseName + ' | 4K'
      }
    })
  })

  fs.writeFileSync('douer_v3_raw.json', JSON.stringify(panOnlyRawResultV3, null, 2))
  fs.writeFileSync('douer_v3.json', JSON.stringify(panOnlyResultV3, null, 2))

  let sourcesCopy = JSON.parse(JSON.stringify(sources))
  let envList = []
  const envSet = new Set() // 用于去重

  sourcesCopy.forEach((item) => {
    if (item.api.includes(kLocalPathTAG)) {
      item.api = item.api.split(kLocalPathTAG)[1]
    }
    if (item.env?.length > 0) {
      const longList = item.env.split('&&')
      longList.forEach((env) => {
        const oneEnv = env.split('##')
        const envKey = oneEnv[0] // 使用环境变量名作为唯一标识

        // 只有当环境变量名不存在时才添加
        if (!envSet.has(envKey)) {
          envSet.add(envKey)
          envList.push({
            name: oneEnv[0],
            desc: oneEnv[1],
            value: '',
          })
        }
      })
    }
  })

  fs.writeFileSync('local.json', JSON.stringify(sourcesCopy, null, 2))
  fs.writeFileSync('env.json', JSON.stringify(envList, null, 2))
  const includePaths = {
    directories: ['danMu', 'panTools', 'recommend', 'vod', 'live', 'cms'],
    files: ['local.json', 'env.json'],
  }

  const shouldInclude = (filePath) => {
    const relativePath = path.relative(process.cwd(), filePath)

    // 过滤掉所有 README.md 文件
    if (path.basename(filePath) === 'README.md') {
      return false
    }

    // 过滤掉指定的 JSON 配置文件
    const excludeJsonFiles = ['douban.json', 'panTools.json', 'danMu.json', 'vod.json']
    if (excludeJsonFiles.includes(path.basename(filePath))) {
      return false
    }

    // 检查是否是允许的文件
    if (includePaths.files.includes(relativePath)) {
      return true
    }

    // 检查是否在指定的目录下
    if (includePaths.directories.some((dir) => relativePath.startsWith(dir))) {
      // 对于 JS 文件，检查是否包含 @deprecated 或 @isAV 标记
      if (filePath.endsWith('.js') || filePath.endsWith('.txt')) {
        try {
          const content = fs.readFileSync(filePath, 'utf8')
          const deprecated = extractValue(content, '@deprecated:')
          const isAV = extractValue(content, '@isAV:')

          // 过滤掉 @deprecated: 1 或 @isAV: 1 的文件
          if ((deprecated && parseInt(deprecated) === 1) || (isAV && parseInt(isAV) === 1)) {
            return false
          }
        } catch (error) {
          // 如果读取文件失败，默认包含
          console.warn(`Warning: Could not read file ${filePath}:`, error.message)
        }
      }
      return true
    }

    return false
  }

  // Create a zip archive
  const output = fs.createWriteStream('uzAio_green.zip')
  const archive = archiver('zip', {
    zlib: { level: 9 }, // Set the compression level
  })

  output.on('close', () => {
    console.log(archive.pointer() + ' total bytes')
    console.log('uzAio_green.zip has been created')
  })

  archive.on('error', (err) => {
    throw err
  })

  archive.on('warning', (err) => {
    if (err.code === 'ENOENT') {
      console.warn('Warning:', err)
    } else {
      throw err
    }
  })

  archive.pipe(output)

  try {
    const walk = (directoryPath) => {
      const files = fs.readdirSync(directoryPath)

      files.forEach((file) => {
        try {
          const filePath = path.join(directoryPath, file)
          const stats = fs.statSync(filePath)

          // 只处理包含的文件和目录
          if (!shouldInclude(filePath)) {
            return
          }

          if (stats.isDirectory()) {
            walk(filePath)
          } else {
            archive.file(filePath, {
              name: path.relative(process.cwd(), filePath),
            })
          }
        } catch (err) {
          console.error(`Error processing ${file}:`, err)
        }
      })
    }

    walk(process.cwd())

    // Finalize the archive
    await archive.finalize()
  } catch (err) {
    console.error('Error creating archive:', err)
    throw err
  }
}



main()
