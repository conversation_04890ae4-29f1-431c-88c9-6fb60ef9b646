# uzVideo 扩展元数据生成器 - Python 版本

## 功能说明

这是 uzVideo 扩展元数据生成器的 Python 版本，用于监控目标仓库更新并自动生成配置文件。

### 主要功能

1. **智能更新检测** - 通过 GitHub API 检测目标仓库是否有新提交
2. **自动下载同步** - 下载目标仓库最新内容到本地
3. **元数据解析** - 解析 JS/TXT 文件中的注释元数据
4. **多格式输出** - 生成多种格式的配置文件
5. **内容过滤** - 自动过滤废弃和不适宜内容
6. **ZIP打包** - 创建绿色版本的压缩包
7. **Git提交推送** - 自动提交生成的文件到当前仓库

### 生成的文件

**配置文件：**
- `local.json` - 本地路径版本配置
- `env.json` - 环境变量配置

**打包文件：**
- `uzAio_green.zip` - 完整版压缩包（包含所有扩展+配置）
- `douer_v2.zip` - 网盘专版v2（表情符号版本，仅网盘相关扩展）
- `douer_v3.zip` - 网盘专版v3（4K标识版本，仅网盘相关扩展）

## 环境要求

- Python 3.7+
- requests 库

## 安装依赖

```bash
pip install requests
```

## 使用方法

### 1. 环境配置

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安装依赖
pip install requests
```

### 2. 运行模式

**正常模式（完整流程）：**
```bash
python uz_metadata_generator.py
```

**本地测试模式（跳过Git操作）：**
```bash
python uz_metadata_generator.py --skip-git
# 或
python uz_metadata_generator.py --test
```

**强制更新模式（跳过更新检查）：**
```bash
python uz_metadata_generator.py --force
```

**完全本地测试（跳过更新检查和Git操作）：**
```bash
python uz_metadata_generator.py --test --force
```

**运行后清理模式（节省磁盘空间）：**
```bash
python uz_metadata_generator.py --cleanup
```

**组合使用（本地测试+清理）：**
```bash
python uz_metadata_generator.py --test --force --cleanup
```

### 3. 定时任务（crontab）

```bash
# 每小时检查一次
0 * * * * /usr/bin/python3 /path/to/uz_metadata_generator.py

# 每6小时检查一次
0 */6 * * * /usr/bin/python3 /path/to/uz_metadata_generator.py
```

### 4. dpanel 计划任务

在 dpanel 面板中添加计划任务：
- **任务名称：** uzVideo 元数据生成
- **执行命令：** `python3 /path/to/uz_metadata_generator.py`
- **执行周期：** 每6小时
- **日志文件：** `/path/to/uz_generator.log`

**注意事项：**
- 确保服务器环境有Git访问权限
- 配置SSH密钥或Personal Access Token
- 确保对工作目录有读写权限

## 配置说明

### 基础配置

编辑 `config.py` 文件可以修改：

- **目标仓库信息** - 修改 `TARGET_REPO` 配置
- **类型映射** - 修改 `TYPE_MAPPING` 配置
- **表情符号** - 修改 `EMOJI_MAP` 配置
- **过滤规则** - 修改 `FILTER_CONFIG` 配置



### Git 配置

**前提条件：**
- 确保运行环境已安装 Git
- 确保当前目录是一个 Git 仓库
- 配置好 Git 远程仓库的推送权限

**自动配置：**
程序会自动配置 Git 用户信息：
- 用户名：`Python Auto Generator`
- 邮箱：`<EMAIL>`

**手动配置（可选）：**
```bash
git config user.name "Your Name"
git config user.email "<EMAIL>"
```

## 日志说明

程序运行时会生成 `uz_generator.log` 日志文件，包含：
- 更新检测结果
- 文件下载进度
- 配置生成状态
- 错误信息记录

## 工作原理

1. **检测更新** - 调用 GitHub API 获取最新 commit hash
2. **对比记录** - 与本地保存的 `last_target_commit.txt` 对比
3. **下载解压** - 有更新时下载目标仓库 ZIP 包并直接解压到工作目录
4. **解析文件** - 扫描 JS/TXT 文件提取元数据（支持跨平台路径处理）
5. **生成配置** - 根据元数据生成各种格式的配置文件
6. **创建压缩包** - 打包源码和配置文件
7. **Git提交推送** - 提交生成的文件到当前仓库（可选）
8. **保存状态** - 更新本地 commit 记录

### 技术特性

- **跨平台兼容** - 自动处理Windows/Linux路径分隔符差异
- **深拷贝保护** - 避免数据修改时的引用问题
- **UTF-8编码** - 正确处理中文日志和文件内容
- **错误恢复** - 完善的异常处理和日志记录

## 优势对比

相比 GitHub Actions + Node.js 版本：

- ✅ **环境简单** - 只需 Python，无需 Node.js
- ✅ **部署灵活** - 可在任意服务器运行
- ✅ **资源占用少** - 无需 GitHub Actions 资源
- ✅ **控制性强** - 可自定义更多逻辑
- ✅ **日志完善** - 更好的错误处理和日志记录

## 故障排除

**常见问题：**

1. **网络连接失败** - 检查网络连接和防火墙设置
2. **权限不足** - 确保脚本有读写权限
3. **依赖缺失** - 运行 `pip install requests` 安装依赖
4. **编码错误** - 确保系统支持 UTF-8 编码

**调试方法：**

```bash
# 查看详细日志
tail -f uz_generator.log

# 本地测试运行（不进行Git操作）
python uz_metadata_generator.py --test --force

# 手动运行查看错误
python uz_metadata_generator.py
```

## 命令行参数

- `--skip-git` 或 `--test` - 跳过Git提交推送操作，用于本地测试
- `--force` - 跳过更新检查，强制执行所有步骤
- `--cleanup` - 运行完成后清理下载的文件，节省磁盘空间
- 组合使用：`--test --force --cleanup` - 完全本地测试+清理模式

### 文件清理策略

**默认模式**（无--cleanup）：
- 保留下载的目录供调试查看
- 下次运行时清理上次的文件

**清理模式**（--cleanup）：
- 运行完成后立即清理所有下载目录
- 保持工作目录整洁，节省磁盘空间

## 更新日志

### v1.2.0 (2025-07-24)
- ✅ 新增douer_v2.zip和douer_v3.zip专版打包
- ✅ 优化local.json排序顺序（panTools→danMu→recommend→live→vod）
- ✅ 修复live数据排序问题，保持原始顺序
- ✅ douer版本精简，移除live和cms相关内容
- ✅ 添加--cleanup参数，支持运行后清理文件
- ✅ 移除冗余的JSON文件生成，直接生成ZIP包

### v1.1.0 (2025-07-24)
- ✅ 修复Windows路径分隔符问题
- ✅ 修复数据深拷贝问题，避免API链接错误
- ✅ 修复日志文件编码和位置问题
- ✅ 优化下载解压流程，直接解压到工作目录
- ✅ 添加本地测试模式支持
- ✅ 完善错误处理和日志记录

### v1.0.0 (2025-07-23)
- 🎉 初始版本发布
- ✅ 100%复刻原版Node.js功能
- ✅ 支持所有配置文件生成
- ✅ 支持ZIP打包和Git提交
